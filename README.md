# AI研发辅助平台

基于Vue 3 + TypeScript的智能化研发辅助平台，通过AI技术提升软件开发效率，支持需求分析、代码评审、测试用例生成等全流程智能化辅助。

## ✨ 核心功能

- 🤖 **AI助手**: 智能对话，支持多种AI模型
- 📋 **需求管理**: 需求文档管理和AI解析优化
- 🔍 **代码Diff**: 代码差异对比和分析
- 🔄 **流水线**: 自动化代码评审和测试用例生成
- ⚙️ **基础配置**: Git、AI模型、Prompt模板配置
- ⌨️ **快捷键系统**: 丰富的快捷键支持，提升操作效率

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm/yarn/pnpm

### 安装和运行
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 🔐 测试账户
- **管理员**: `admin` / `admin123`
- **开发者**: `developer` / `dev123`
- **测试员**: `tester` / `test123`
- **普通用户**: `user` / `user123`

### ⌨️ 快捷键
- `Ctrl + /` - 显示快捷键帮助
- `Ctrl + Shift + A` - 打开AI助手
- `Ctrl + Shift + O` - AI优化内容（编辑器中）

## 🛠️ 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **构建工具**: Vite
- **编辑器**: md-editor-v3
- **样式**: Sass/SCSS

## 📚 文档

- [产品需求文档 (PRD)](./docs/PRD_v2.md)
- [技术设计文档](./docs/TechnicalDesign.md)
- [API接口文档](./docs/API.md)
- [部署指南](./docs/Deployment.md)
- [用户使用手册](./docs/UserGuide.md)
- [登录信息](./docs/LoginInfo.md)

## 🎯 主要特性

### AI集成功能
- 支持6大主流AI服务商
- 智能Prompt模板系统
- 实时AI优化功能

### 用户体验
- 响应式设计
- 丰富的快捷键支持
- 直观的操作界面

### 开发体验
- TypeScript类型安全
- 组件化架构
- 热重载开发

## 📄 许可证

MIT License
