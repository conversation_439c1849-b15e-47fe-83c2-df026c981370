# AI研发辅助平台 - API接口文档

## 📋 接口概述

### 基础信息
- **API版本**: v1.0
- **基础URL**: `https://api.example.com/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-12-01T10:00:00Z"
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ],
  "timestamp": "2024-12-01T10:00:00Z"
}
```

### 状态码说明
- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权，需要登录
- `403` - 禁止访问，权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

## 🔐 认证接口

### 用户登录
```
POST /auth/login
```

**请求参数**
```json
{
  "username": "string",
  "password": "string"
}
```

**响应数据**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "1",
      "username": "admin",
      "email": "<EMAIL>",
      "roles": ["admin"]
    },
    "expiresIn": 86400
  }
}
```

### 用户登出
```
POST /auth/logout
```

**请求头**
```
Authorization: Bearer {token}
```

**响应数据**
```json
{
  "code": 200,
  "message": "登出成功"
}
```

### 刷新Token
```
POST /auth/refresh
```

**请求头**
```
Authorization: Bearer {token}
```

**响应数据**
```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "new_token_here",
    "expiresIn": 86400
  }
}
```

## 👤 用户管理接口

### 获取用户信息
```
GET /users/profile
```

**请求头**
```
Authorization: Bearer {token}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "id": "1",
    "username": "admin",
    "email": "<EMAIL>",
    "nickname": "管理员",
    "avatar": "https://example.com/avatar.jpg",
    "department": "技术部",
    "position": "高级工程师",
    "roles": ["admin"],
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### 更新用户信息
```
PUT /users/profile
```

**请求参数**
```json
{
  "email": "<EMAIL>",
  "nickname": "新昵称",
  "department": "产品部",
  "position": "产品经理"
}
```

### 修改密码
```
PUT /users/password
```

**请求参数**
```json
{
  "currentPassword": "old_password",
  "newPassword": "new_password"
}
```

### 上传头像
```
POST /users/avatar
```

**请求格式**: `multipart/form-data`
**请求参数**
- `file`: 图片文件 (支持jpg, png, gif，最大2MB)

**响应数据**
```json
{
  "code": 200,
  "data": {
    "url": "https://example.com/avatars/user_1_avatar.jpg"
  }
}
```

## 🤖 AI模型配置接口

### 获取模型配置列表
```
GET /ai/models
```

**查询参数**
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 20)
- `provider`: 服务商筛选
- `isActive`: 是否启用

**响应数据**
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "1",
        "name": "OpenAI GPT-4o",
        "provider": "openai",
        "baseUrl": "https://api.openai.com/v1",
        "model": "gpt-4o",
        "maxTokens": 4096,
        "temperature": 0.7,
        "isDefault": true,
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 20
  }
}
```

### 创建模型配置
```
POST /ai/models
```

**请求参数**
```json
{
  "name": "DeepSeek Chat",
  "provider": "deepseek",
  "baseUrl": "https://api.deepseek.com/v1",
  "apiKey": "sk-xxx",
  "model": "deepseek-chat",
  "maxTokens": 4096,
  "temperature": 0.7,
  "isDefault": false,
  "isActive": true
}
```

### 更新模型配置
```
PUT /ai/models/{id}
```

### 删除模型配置
```
DELETE /ai/models/{id}
```

### 测试模型连接
```
POST /ai/models/{id}/test
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "success": true,
    "message": "连接测试成功",
    "latency": 150
  }
}
```

### 设置默认模型
```
POST /ai/models/{id}/set-default
```

## 📝 Prompt模板接口

### 获取模板列表
```
GET /ai/prompts
```

**查询参数**
- `category`: 分类筛选
- `tags`: 标签筛选 (多个用逗号分隔)
- `isPublic`: 是否公开
- `keyword`: 关键词搜索

**响应数据**
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "1",
        "name": "代码评审专家",
        "identifier": "code_review_expert",
        "content": "你是一个资深的代码评审专家...",
        "description": "专业的代码评审模板",
        "category": "code_review",
        "tags": ["代码评审", "安全检查"],
        "variables": ["code_diff", "requirement"],
        "isPublic": true,
        "usageCount": 156,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

### 创建模板
```
POST /ai/prompts
```

**请求参数**
```json
{
  "name": "需求分析师",
  "identifier": "requirement_analyst",
  "content": "你是一个专业的需求分析师，请分析以下需求：\n\n{{requirement}}",
  "description": "专业的需求分析模板",
  "category": "requirement",
  "tags": ["需求分析", "功能设计"],
  "isPublic": true
}
```

### 渲染模板
```
POST /ai/prompts/{id}/render
```

**请求参数**
```json
{
  "variables": {
    "requirement": "用户登录功能需求",
    "code_diff": "diff --git a/login.js..."
  }
}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "content": "你是一个专业的需求分析师，请分析以下需求：\n\n用户登录功能需求"
  }
}
```

## 💬 AI对话接口

### 获取对话列表
```
GET /ai/conversations
```

**响应数据**
```json
{
  "code": 200,
  "data": [
    {
      "id": "1",
      "title": "代码评审讨论",
      "modelConfigId": "1",
      "totalTokens": 1250,
      "messageCount": 8,
      "lastMessageAt": "2024-01-01T10:30:00Z",
      "createdAt": "2024-01-01T10:00:00Z"
    }
  ]
}
```

### 创建对话
```
POST /ai/conversations
```

**请求参数**
```json
{
  "title": "新的对话",
  "modelConfigId": "1"
}
```

### 获取对话详情
```
GET /ai/conversations/{id}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "id": "1",
    "title": "代码评审讨论",
    "modelConfigId": "1",
    "messages": [
      {
        "id": "1",
        "role": "user",
        "content": "请帮我评审这段代码",
        "timestamp": "2024-01-01T10:00:00Z",
        "tokens": 10
      },
      {
        "id": "2",
        "role": "assistant",
        "content": "这段代码整体结构清晰...",
        "timestamp": "2024-01-01T10:01:00Z",
        "tokens": 150
      }
    ],
    "totalTokens": 160
  }
}
```

### 发送消息
```
POST /ai/conversations/{id}/messages
```

**请求参数**
```json
{
  "content": "请帮我分析这个需求",
  "modelConfigId": "1",
  "promptTemplateId": "2",
  "context": {
    "requirement": "用户登录功能",
    "code_diff": "diff内容"
  }
}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "id": "3",
    "role": "assistant",
    "content": "根据您提供的需求，我分析如下...",
    "timestamp": "2024-01-01T10:02:00Z",
    "tokens": 200
  }
}
```

### 流式发送消息
```
POST /ai/conversations/{id}/messages/stream
```

**响应格式**: `text/event-stream`

**事件格式**
```
data: {"type": "chunk", "content": "根据您"}
data: {"type": "chunk", "content": "提供的需求"}
data: {"type": "done", "totalTokens": 200}
```

## 📋 需求管理接口

### 获取需求列表
```
GET /requirements
```

**查询参数**
- `status`: 状态筛选
- `source`: 来源筛选
- `keyword`: 关键词搜索

**响应数据**
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "1",
        "name": "用户登录功能需求",
        "originalContent": "用户需要能够登录系统...",
        "optimizedContent": "## 用户登录功能需求\n\n### 功能描述...",
        "source": "upload",
        "originalFilename": "login_requirement.md",
        "status": "completed",
        "parseTaskId": "task_1",
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

### 创建需求
```
POST /requirements
```

**请求参数**
```json
{
  "name": "支付功能需求",
  "originalContent": "用户需要能够在线支付...",
  "source": "manual"
}
```

### 上传需求文档
```
POST /requirements/upload
```

**请求格式**: `multipart/form-data`
**请求参数**
- `file`: 需求文档文件
- `name`: 需求名称

### 解析需求
```
POST /requirements/{id}/parse
```

**请求参数**
```json
{
  "modelConfigId": "1",
  "promptTemplateId": "2"
}
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "taskId": "parse_task_123",
    "status": "running"
  }
}
```

## 🔍 代码Diff接口

### 获取Diff任务列表
```
GET /code-diff/tasks
```

### 创建Diff任务
```
POST /code-diff/tasks
```

**请求参数**
```json
{
  "name": "登录功能代码对比",
  "repositoryId": "1",
  "compareType": "branch",
  "sourceRef": "feature/login",
  "targetRef": "main"
}
```

### 执行Diff任务
```
POST /code-diff/tasks/{id}/execute
```

### 获取Diff结果
```
GET /code-diff/tasks/{id}/result
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "diffContent": "diff --git a/login.js b/login.js\nindex 1234567..abcdefg 100644\n--- a/login.js\n+++ b/login.js\n@@ -10,7 +10,7 @@ function login(username, password) {\n   if (!username || !password) {\n-    throw new Error('用户名和密码不能为空');\n+    return { success: false, message: '用户名和密码不能为空' };\n   }",
    "stats": {
      "filesChanged": 1,
      "insertions": 1,
      "deletions": 1
    }
  }
}
```

## 🔄 流水线接口

### 获取流水线任务列表
```
GET /pipelines/tasks
```

### 创建流水线任务
```
POST /pipelines/tasks
```

**请求参数**
```json
{
  "name": "用户登录代码评审",
  "type": "code_review",
  "codeDiffTaskId": "1",
  "requirementDocumentId": "1",
  "promptTemplateId": "1",
  "knowledgeBaseId": "1"
}
```

### 执行流水线任务
```
POST /pipelines/tasks/{id}/execute
```

### 获取任务结果
```
GET /pipelines/tasks/{id}/result
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "result": "# 代码评审报告\n\n## 总体评价\n代码质量良好...",
    "issues": [
      {
        "type": "security",
        "severity": "high",
        "message": "发现SQL注入风险",
        "line": 25,
        "file": "login.js"
      }
    ],
    "suggestions": [
      "使用参数化查询防止SQL注入",
      "添加输入验证"
    ]
  }
}
```

## 📊 任务状态接口

### 获取任务状态
```
GET /tasks/{taskId}/status
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "status": "running",
    "progress": 65,
    "message": "正在分析代码差异...",
    "startTime": "2024-01-01T10:00:00Z",
    "estimatedTime": 120
  }
}
```

### 取消任务
```
POST /tasks/{taskId}/cancel
```

## 📁 文件上传接口

### 通用文件上传
```
POST /upload
```

**请求格式**: `multipart/form-data`
**请求参数**
- `file`: 文件
- `type`: 文件类型 (avatar, document, attachment)

**响应数据**
```json
{
  "code": 200,
  "data": {
    "url": "https://example.com/files/xxx.jpg",
    "filename": "original_name.jpg",
    "size": 1024000,
    "mimeType": "image/jpeg"
  }
}
```

## 🔧 系统配置接口

### 获取系统配置
```
GET /system/config
```

### Git配置接口
```
GET /git/credentials
POST /git/credentials
PUT /git/credentials/{id}
DELETE /git/credentials/{id}

GET /git/repositories
POST /git/repositories
PUT /git/repositories/{id}
DELETE /git/repositories/{id}
POST /git/repositories/{id}/test
```

### 知识库接口
```
GET /knowledge-base
POST /knowledge-base
PUT /knowledge-base/{id}
DELETE /knowledge-base/{id}
POST /knowledge-base/{id}/documents
GET /knowledge-base/{id}/search?q=keyword
```

## 📈 统计分析接口

### 获取仪表盘数据
```
GET /dashboard/stats
```

**响应数据**
```json
{
  "code": 200,
  "data": {
    "totalTasks": 156,
    "completedTasks": 142,
    "runningTasks": 8,
    "failedTasks": 6,
    "totalTokensUsed": 125000,
    "averageResponseTime": 2.5,
    "recentTasks": [
      {
        "id": "1",
        "name": "代码评审任务",
        "type": "code_review",
        "status": "completed",
        "createdAt": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

### 获取使用统计
```
GET /stats/usage
```

**查询参数**
- `startDate`: 开始日期
- `endDate`: 结束日期
- `granularity`: 粒度 (day, week, month)

---

**文档版本**：v2.0  
**最后更新**：2024年12月  
**负责人**：后端团队
