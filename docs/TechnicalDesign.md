# AI研发辅助平台 - 技术设计文档

## 📋 文档概述

### 文档目的
本文档详细描述AI研发辅助平台的技术架构、系统设计、接口规范和实现细节，为开发团队提供技术指导。

### 适用范围
- 前端开发团队
- 后端开发团队
- 测试团队
- 运维团队

## 🏗️ 系统架构

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                     用户界面层                                │
│  Vue3 + TypeScript + Element Plus + Pinia + Vue Router     │
├─────────────────────────────────────────────────────────────┤
│                     API网关层                                │
│  Nginx + API Gateway + 负载均衡 + 限流 + 鉴权               │
├─────────────────────────────────────────────────────────────┤
│                    业务服务层                                │
│  用户服务 + 配置服务 + 需求服务 + 代码服务 + 流水线服务      │
├─────────────────────────────────────────────────────────────┤
│                   AI模型集成层                               │
│  OpenAI + DeepSeek + 星火 + 豆包 + Gemini + Claude         │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                                │
│  MySQL + Redis + 向量数据库 + 对象存储                      │
└─────────────────────────────────────────────────────────────┘
```

### 技术选型

#### 前端技术栈
- **框架**：Vue 3.5+ (Composition API)
- **语言**：TypeScript 5.0+
- **UI库**：Element Plus 2.10+
- **状态管理**：Pinia 3.0+
- **路由**：Vue Router 4.5+
- **构建工具**：Vite 6.2+
- **样式**：Sass/SCSS
- **编辑器**：md-editor-v3 (Markdown编辑器)
- **HTTP客户端**：Axios 1.9+

#### 开发工具
- **代码质量**：ESLint + Prettier
- **类型检查**：TypeScript 严格模式
- **版本控制**：Git
- **包管理**：npm/yarn/pnpm

## 📁 项目结构

### 目录结构
```
front-platform/
├── public/                     # 静态资源
├── src/
│   ├── api/                    # API服务层
│   │   ├── index.ts           # Axios配置和拦截器
│   │   ├── ai.ts              # AI相关API
│   │   ├── user.ts            # 用户相关API
│   │   ├── git.ts             # Git配置API
│   │   └── ...
│   ├── assets/                 # 静态资源
│   │   ├── styles/            # 全局样式
│   │   │   └── main.scss      # 主样式文件
│   │   └── logo.svg           # Logo文件
│   ├── components/             # 组件
│   │   ├── common/            # 通用组件
│   │   │   ├── MarkdownEditor.vue  # 增强版Markdown编辑器
│   │   │   ├── ShortcutHelp.vue    # 快捷键帮助组件
│   │   │   └── ErrorBoundary.vue
│   │   └── layout/            # 布局组件
│   │       ├── Layout.vue
│   │       ├── Header.vue     # 增强版Header（AI快捷入口）
│   │       └── Sidebar.vue    # 优化样式的侧边栏
│   ├── composables/           # 组合式函数
│   │   ├── useKeyboard.ts     # 快捷键管理Hook
│   │   ├── usePolling.ts      # 轮询Hook
│   │   └── useDebounce.ts     # 防抖Hook
│   ├── router/                # 路由配置
│   │   └── index.ts
│   ├── stores/                # 状态管理
│   │   ├── index.ts           # Pinia配置
│   │   ├── user.ts            # 用户状态
│   │   ├── app.ts             # 应用状态
│   │   └── ai.ts              # AI状态
│   ├── types/                 # 类型定义
│   │   ├── index.ts           # 类型导出
│   │   ├── models.ts          # 数据模型
│   │   └── api.ts             # API类型
│   ├── utils/                 # 工具函数
│   │   └── formatter.ts       # 格式化工具
│   ├── views/                 # 页面组件
│   │   ├── Login.vue          # 登录页
│   │   ├── Dashboard.vue      # 仪表盘
│   │   ├── Profile.vue        # 个人信息
│   │   ├── ai-chat/           # AI助手
│   │   ├── requirements/      # 需求管理
│   │   ├── code-diff/         # 代码Diff
│   │   ├── pipelines/         # 流水线
│   │   └── configuration/     # 基础配置
│   ├── App.vue                # 根组件
│   └── main.ts                # 应用入口
├── docs/                      # 文档
├── .env.development           # 开发环境变量
├── .env.production            # 生产环境变量
├── package.json               # 依赖配置
├── tsconfig.json              # TypeScript配置
├── vite.config.ts             # Vite配置
└── README.md                  # 项目说明
```

## 🔧 核心模块设计

### 1. API服务层设计

#### Axios配置 (`src/api/index.ts`)
```typescript
import axios, { InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => response.data,
  (error) => {
    const { response } = error
    if (response) {
      switch (response.status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          localStorage.removeItem('token')
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('没有权限访问')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(response.data?.message || '请求失败')
      }
    }
    return Promise.reject(error)
  }
)
```

#### AI服务API (`src/api/ai.ts`)
```typescript
import { api } from './index'
import type { AIModelConfig, AIMessage, PromptTemplate } from '@/types'

export const aiModelApi = {
  getModelConfigs: () => api.get<AIModelConfig[]>('/ai/models'),
  createModelConfig: (data: Omit<AIModelConfig, 'id' | 'createdAt' | 'updatedAt'>) =>
    api.post<AIModelConfig>('/ai/models', data),
  testModelConnection: (id: string) =>
    api.post<{ success: boolean; message: string }>(`/ai/models/${id}/test`)
}

export const aiChatApi = {
  sendMessage: (conversationId: string, data: {
    content: string
    modelConfigId: string
    promptTemplateId?: string
  }) => api.post<AIMessage>(`/ai/conversations/${conversationId}/messages`, data)
}
```

### 2. 状态管理设计

#### Pinia Store结构
```typescript
// src/stores/ai.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AIModelConfig, AIConversation } from '@/types'

export const useAIStore = defineStore('ai', () => {
  // 状态
  const modelConfigs = ref<AIModelConfig[]>([])
  const conversations = ref<AIConversation[]>([])
  
  // 计算属性
  const defaultModel = computed(() => 
    modelConfigs.value.find(model => model.isDefault && model.isActive)
  )
  
  // 方法
  const loadModelConfigs = async () => {
    // API调用逻辑
  }
  
  return {
    modelConfigs,
    conversations,
    defaultModel,
    loadModelConfigs
  }
})
```

### 3. 路由设计

#### 路由配置 (`src/router/index.ts`)
```typescript
import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: () => import('@/components/layout/Layout.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 导航守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta?.requiresAuth !== false && !userStore.isLoggedIn) {
    next('/login')
    return
  }
  
  next()
})
```

### 4. 组件设计

#### 布局组件 (`src/components/layout/Layout.vue`)
```vue
<template>
  <div class="layout-container">
    <Header />
    <div class="layout-main">
      <Sidebar />
      <main class="content-area">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from './Header.vue'
import Sidebar from './Sidebar.vue'
</script>
```

#### 增强版Markdown编辑器组件 (`src/components/common/MarkdownEditor.vue`)
```vue
<template>
  <div class="markdown-editor">
    <MdEditor
      v-model="content"
      :height="height"
      :preview="preview"
      :toolbars="toolbars"
      @save="handleSave"
      @upload-img="handleUploadImg"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { MdEditor } from 'md-editor-v3'

interface Props {
  modelValue: string
  height?: string
  preview?: boolean
  enableAiOptimize?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  preview: true,
  enableAiOptimize: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'save': [value: string]
  'ai-optimize': [value: string]
}>()

// 动态工具栏配置，支持AI优化按钮
const toolbars = computed(() => {
  const baseToolbars = ['bold', 'italic', 'title', 'quote', 'unorderedList', 'orderedList', 'codeRow', 'code', 'link', 'image', 'table', 'save']

  if (props.enableAiOptimize) {
    baseToolbars.push('-', {
      title: 'AI优化',
      icon: 'icon-ai-optimize',
      action: handleAiOptimize
    })
  }

  return baseToolbars
})

const content = ref(props.modelValue)

const handleAiOptimize = () => {
  emit('ai-optimize', content.value)
}

watch(() => content.value, (newValue) => {
  emit('update:modelValue', newValue)
})
</script>
```

### 5. 快捷键系统设计

#### 快捷键管理组合式函数 (`src/composables/useKeyboard.ts`)
```typescript
export interface KeyboardShortcut {
  key: string
  ctrl?: boolean
  alt?: boolean
  shift?: boolean
  meta?: boolean
  action: () => void
  description: string
}

export function useKeyboard() {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'k',
      ctrl: true,
      action: () => openGlobalSearch(),
      description: 'Ctrl + K: 全局搜索'
    },
    {
      key: 'a',
      ctrl: true,
      shift: true,
      action: () => router.push('/ai-chat'),
      description: 'Ctrl + Shift + A: 打开AI助手'
    }
    // ... 更多快捷键
  ]

  const handleKeyDown = (event: KeyboardEvent) => {
    // 智能焦点检测，避免输入冲突
    const isInputFocused = document.activeElement && (
      document.activeElement.tagName === 'INPUT' ||
      document.activeElement.tagName === 'TEXTAREA' ||
      document.activeElement.contentEditable === 'true'
    )

    if (isInputFocused) {
      // 在输入框中只处理特定快捷键
      const allowedInInput = ['k', '/']
      const matchedShortcut = shortcuts.find(shortcut =>
        isShortcutMatch(event, shortcut) && allowedInInput.includes(shortcut.key)
      )

      if (matchedShortcut) {
        event.preventDefault()
        matchedShortcut.action()
      }
      return
    }

    // 查找匹配的快捷键
    const matchedShortcut = shortcuts.find(shortcut => isShortcutMatch(event, shortcut))
    if (matchedShortcut) {
      event.preventDefault()
      matchedShortcut.action()
    }
  }

  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown)
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
  })

  return { shortcuts, addShortcut, removeShortcut }
}
```

#### 快捷键帮助组件 (`src/components/common/ShortcutHelp.vue`)
- **分类展示**：按功能模块分类显示快捷键
- **视觉设计**：键盘按键样式的快捷键显示
- **打印支持**：支持打印快捷键参考表
- **响应式设计**：适配不同屏幕尺寸

#### 事件驱动架构
```typescript
// 使用自定义事件实现组件间通信
document.addEventListener('ai-send-message', handleSendMessage)
document.addEventListener('editor-save', handleSave)
document.addEventListener('editor-ai-optimize', handleAiOptimize)
```

## 🔗 接口设计

### API接口规范

#### 请求格式
```
GET /api/v1/resource
POST /api/v1/resource
PUT /api/v1/resource/:id
DELETE /api/v1/resource/:id
```

#### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-12-01T10:00:00Z"
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ],
  "timestamp": "2024-12-01T10:00:00Z"
}
```

### 核心接口定义

#### 用户认证接口
```typescript
// POST /api/v1/auth/login
interface LoginRequest {
  username: string
  password: string
}

interface LoginResponse {
  token: string
  user: UserInfo
  expiresIn: number
}
```

#### AI模型配置接口
```typescript
// GET /api/v1/ai/models
interface GetModelsResponse {
  models: AIModelConfig[]
}

// POST /api/v1/ai/models
interface CreateModelRequest {
  name: string
  provider: string
  baseUrl: string
  apiKey: string
  model: string
}
```

#### AI对话接口
```typescript
// POST /api/v1/ai/conversations/:id/messages
interface SendMessageRequest {
  content: string
  modelConfigId: string
  promptTemplateId?: string
  context?: Record<string, any>
}

interface SendMessageResponse {
  message: AIMessage
}
```

## 🎨 UI/UX设计规范

### 设计系统

#### 颜色规范
```scss
// 主色调
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 中性色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #C0C4CC;

// 背景色
$bg-color: #FFFFFF;
$bg-color-page: #F2F3F5;
$bg-color-overlay: #FFFFFF;
```

#### 字体规范
```scss
// 字体大小
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

// 字体权重
$font-weight-primary: 500;
$font-weight-secondary: 400;
```

#### 间距规范
```scss
// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
```

### 组件规范

#### 按钮规范
- **主要按钮**：用于主要操作，如提交、确认
- **次要按钮**：用于次要操作，如取消、重置
- **文本按钮**：用于辅助操作，如编辑、删除

#### 表单规范
- **标签对齐**：左对齐，宽度120px
- **必填标识**：红色星号(*)
- **验证提示**：实时验证，错误信息红色显示
- **帮助文本**：灰色小字，位于输入框下方

#### 表格规范
- **表头**：加粗显示，背景色区分
- **行高**：48px，提供足够的点击区域
- **分页**：底部右对齐，显示总数和页码
- **操作列**：固定宽度，按钮间距8px

## 🔒 安全设计

### 前端安全

#### XSS防护
```typescript
// 输入过滤
const sanitizeInput = (input: string): string => {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
}

// 使用v-html时的安全处理
import DOMPurify from 'dompurify'

const safeHtml = computed(() => {
  return DOMPurify.sanitize(rawHtml.value)
})
```

#### CSRF防护
```typescript
// 请求头添加CSRF Token
request.interceptors.request.use((config) => {
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
  if (csrfToken) {
    config.headers['X-CSRF-TOKEN'] = csrfToken
  }
  return config
})
```

#### 敏感信息保护
```typescript
// 敏感信息不存储在localStorage
const secureStorage = {
  setItem: (key: string, value: string) => {
    // 使用sessionStorage或加密存储
    sessionStorage.setItem(key, btoa(value))
  },
  getItem: (key: string) => {
    const value = sessionStorage.getItem(key)
    return value ? atob(value) : null
  }
}
```

## 📊 性能优化

### 代码分割
```typescript
// 路由懒加载
const routes = [
  {
    path: '/dashboard',
    component: () => import('@/views/Dashboard.vue')
  }
]

// 组件懒加载
const LazyComponent = defineAsyncComponent(() => import('./HeavyComponent.vue'))
```

### 缓存策略
```typescript
// API响应缓存
const apiCache = new Map()

const cachedRequest = async (url: string) => {
  if (apiCache.has(url)) {
    return apiCache.get(url)
  }
  
  const response = await api.get(url)
  apiCache.set(url, response)
  
  // 5分钟后清除缓存
  setTimeout(() => apiCache.delete(url), 5 * 60 * 1000)
  
  return response
}
```

### 虚拟滚动
```vue
<template>
  <el-table-v2
    :columns="columns"
    :data="data"
    :width="700"
    :height="400"
    fixed
  />
</template>
```

## 🧪 测试策略

### 单元测试
```typescript
// 使用Vitest进行单元测试
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import MyComponent from '@/components/MyComponent.vue'

describe('MyComponent', () => {
  it('renders correctly', () => {
    const wrapper = mount(MyComponent, {
      props: { title: 'Test Title' }
    })
    expect(wrapper.text()).toContain('Test Title')
  })
})
```

### E2E测试
```typescript
// 使用Playwright进行E2E测试
import { test, expect } from '@playwright/test'

test('user can login', async ({ page }) => {
  await page.goto('/login')
  await page.fill('[data-testid="username"]', 'testuser')
  await page.fill('[data-testid="password"]', 'password')
  await page.click('[data-testid="login-button"]')
  await expect(page).toHaveURL('/dashboard')
})
```

## 🚀 部署配置

### 构建配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  }
})
```

### 环境配置
```bash
# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=AI研发辅助平台
VITE_APP_ENV=production
```

### Docker配置
```dockerfile
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

---

**文档版本**：v2.0  
**最后更新**：2024年12月  
**负责人**：技术团队
