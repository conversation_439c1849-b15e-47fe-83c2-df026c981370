# AI研发辅助平台 - 登录信息

## 🔐 测试账户信息

### 管理员账户
- **用户名**: `admin`
- **密码**: `admin123`
- **权限**: 管理员权限，可访问所有功能
- **描述**: 系统管理员账户，用于系统配置和用户管理

### 开发者账户
- **用户名**: `developer`
- **密码**: `dev123`
- **权限**: 开发者权限，可使用所有开发功能
- **描述**: 开发人员账户，用于日常开发和测试

### 测试账户
- **用户名**: `tester`
- **密码**: `test123`
- **权限**: 测试权限，可使用基础功能
- **描述**: 测试人员账户，用于功能测试和验证

### 普通用户账户
- **用户名**: `user`
- **密码**: `user123`
- **权限**: 普通用户权限，基础功能访问
- **描述**: 普通用户账户，用于体验基础功能

## 🚀 快速登录

### 访问地址
- **开发环境**: http://localhost:3000
- **登录页面**: http://localhost:3000/login

### 登录步骤
1. 打开浏览器，访问 http://localhost:3000
2. 系统会自动跳转到登录页面
3. 输入上述任一账户的用户名和密码
4. 点击"登录"按钮
5. 登录成功后跳转到仪表盘页面

### 功能体验建议

#### 使用管理员账户 (`admin` / `admin123`)
- ✅ 配置AI模型（OpenAI、DeepSeek等）
- ✅ 管理Prompt模板
- ✅ 配置Git仓库
- ✅ 管理知识库
- ✅ 查看所有用户数据

#### 使用开发者账户 (`developer` / `dev123`)
- ✅ 创建和管理需求文档
- ✅ 使用AI助手进行对话
- ✅ 创建代码Diff任务
- ✅ 执行流水线任务（代码评审、测试用例生成）
- ✅ 使用Markdown编辑器的AI优化功能

#### 使用测试账户 (`tester` / `test123`)
- ✅ 体验需求管理功能
- ✅ 测试AI助手对话
- ✅ 验证快捷键功能
- ✅ 测试用户界面和交互

## ⌨️ 快捷键体验

登录后可以立即体验以下快捷键：

### 全局快捷键
- `Ctrl + /` - 显示快捷键帮助
- `Ctrl + Shift + A` - 打开AI助手
- `Ctrl + Shift + R` - 需求管理
- `Ctrl + Shift + D` - 代码Diff
- `Ctrl + Shift + P` - 流水线
- `Ctrl + H` - 返回首页

### AI助手快捷键（在AI助手页面）
- `Ctrl + Enter` - 发送消息
- `Ctrl + N` - 新建对话
- `Ctrl + L` - 清空对话

### 编辑器快捷键（在需求编辑时）
- `Ctrl + S` - 保存文档
- `Ctrl + Shift + O` - AI优化内容
- `Ctrl + B` - 加粗文本
- `Ctrl + I` - 斜体文本

## 🎯 功能演示路径

### 1. AI助手体验
1. 登录后点击Header右上角的"AI助手"按钮
2. 或使用快捷键 `Ctrl + Shift + A`
3. 选择AI模型（需要先配置）
4. 选择Prompt模板
5. 输入问题并发送消息

### 2. 需求管理体验
1. 导航到"需求管理" → "需求列表"
2. 点击"手动创建需求"
3. 输入需求内容
4. 点击"AI解析"进行优化
5. 在编辑模式下使用AI优化功能

### 3. Markdown编辑器AI优化
1. 在需求管理中编辑任一需求
2. 点击"编辑"按钮
3. 在Markdown编辑器中输入内容
4. 点击工具栏的"AI优化"按钮
5. 或使用快捷键 `Ctrl + Shift + O`

### 4. 快捷键帮助
1. 按 `Ctrl + /` 打开快捷键帮助
2. 或点击Header中的"?"按钮
3. 查看完整的快捷键列表
4. 可以打印快捷键参考表

## 🔧 开发环境配置

### 启动开发服务器
```bash
# 进入项目目录
cd E:/web/front-platform

# 安装依赖（如果还没有安装）
npm install

# 启动开发服务器
npm run dev
```

### 访问应用
- 开发服务器启动后，访问 http://localhost:3000
- 支持热重载，修改代码后自动刷新
- 支持Vue DevTools调试

## 📝 注意事项

### 数据说明
- 当前使用模拟数据，所有操作都是前端模拟
- 登录状态保存在浏览器本地存储中
- 刷新页面后需要重新登录

### AI功能说明
- AI模型配置功能已实现，但需要真实的API密钥
- AI对话和优化功能使用模拟响应
- 可以体验完整的交互流程

### 浏览器兼容性
- 推荐使用 Chrome 90+ 或 Firefox 88+
- 支持现代浏览器的所有功能
- 响应式设计，支持不同屏幕尺寸

## 🆘 常见问题

### Q: 登录失败怎么办？
A: 请确认用户名和密码正确，区分大小写。如果仍然失败，请刷新页面重试。

### Q: 快捷键不生效？
A: 请确认焦点不在输入框中，某些快捷键在输入状态下会被禁用以避免冲突。

### Q: AI功能无响应？
A: 当前AI功能使用模拟数据，会有2-3秒的模拟延迟，请耐心等待。

### Q: 页面显示异常？
A: 请尝试清除浏览器缓存，或使用无痕模式访问。

---

**文档版本**: v2.0  
**最后更新**: 2024年12月  
**维护人员**: 开发团队
