# AI研发辅助平台 - 用户使用手册

## 📋 平台概述

### 平台简介
AI研发辅助平台是一个基于大模型的智能化研发工具，旨在通过AI技术提升软件开发效率。平台支持需求分析、代码评审、测试用例生成等全流程智能化辅助功能。

### 核心功能
- 🤖 **AI助手**：智能对话，支持多种AI模型
- 📋 **需求管理**：需求文档管理和AI解析优化
- 🔍 **代码Diff**：代码差异对比和分析
- 🔄 **流水线**：自动化代码评审和测试用例生成
- ⚙️ **基础配置**：Git、AI模型、Prompt模板配置
- 👤 **个人中心**：用户信息和偏好设置

### 系统要求
- **浏览器**：Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **网络**：稳定的互联网连接
- **分辨率**：最小1280x720，推荐1920x1080

## 🚀 快速开始

### 1. 登录系统
1. 打开浏览器，访问 http://localhost:3000
2. 输入测试账户信息：
   - **管理员**: `admin` / `admin123`
   - **开发者**: `developer` / `dev123`
   - **测试员**: `tester` / `test123`
   - **普通用户**: `user` / `user123`
3. 点击"登录"按钮
4. 首次登录建议完善个人信息

### 2. 配置AI模型
在使用AI功能前，需要先配置AI模型：

1. 进入 **基础配置** → **AI模型配置**
2. 点击"添加模型配置"
3. 选择服务商（OpenAI、DeepSeek等）
4. 填写API配置信息
5. 测试连接并保存

### 3. 创建第一个需求
1. 进入 **需求管理** → **需求列表**
2. 点击"手动创建需求"或"上传需求文档"
3. 填写需求信息
4. 使用AI解析功能优化需求

## 🤖 AI助手使用指南

### 功能概述
AI助手是平台的核心功能，支持智能对话、Prompt模板应用、多模型切换等功能。

### 基本操作

#### 1. 创建新对话
1. 进入 **AI助手** 页面
2. 点击"新建对话"按钮
3. 系统自动创建新的对话会话

#### 2. 选择AI模型
1. 在对话界面顶部选择AI模型
2. 可以实时切换不同的模型
3. 建议根据任务类型选择合适的模型：
   - **代码相关**：DeepSeek Coder
   - **通用对话**：GPT-4o
   - **中文优化**：讯飞星火

#### 3. 使用Prompt模板
1. 点击"Prompt模板"下拉框
2. 根据分类选择合适的模板：
   - **需求分析**：用于需求文档分析
   - **代码评审**：用于代码质量检查
   - **测试用例**：用于测试用例生成
   - **通用对话**：用于日常交流
3. 选择模板后会自动填入输入框

#### 4. 插入变量
使用工具栏快速插入常用变量：
- **代码Diff**：插入 `{{code_diff}}` 变量
- **需求文档**：插入 `{{requirement}}` 变量
- **上下文**：插入 `{{context}}` 变量

#### 5. 发送消息
1. 在输入框中输入问题或需求
2. 使用 `Ctrl + Enter` 快速发送
3. 等待AI回复，支持流式显示

### 高级功能

#### 对话管理
- **重命名对话**：点击对话标题进行重命名
- **删除对话**：右键菜单选择删除
- **导出对话**：将对话内容导出为Markdown

#### Token统计
- 每条消息显示Token消耗
- 对话总Token数实时统计
- 帮助控制API成本

## 📋 需求管理使用指南

### 功能概述
需求管理模块支持需求文档的创建、编辑、AI解析和版本对比功能。

### 基本操作

#### 1. 上传需求文档
1. 进入 **需求管理** → **需求列表**
2. 点击"上传需求文档"
3. 选择文档文件（支持.txt, .md, .doc, .docx, .pdf）
4. 填写需求名称
5. 点击"上传并解析"

#### 2. 手动创建需求
1. 点击"手动创建需求"
2. 填写需求名称
3. 在文本框中输入需求内容
4. 点击"创建"

#### 3. AI需求解析
1. 选择待解析的需求
2. 点击"AI解析"按钮
3. 等待AI处理完成
4. 查看优化后的需求内容

#### 4. 需求编辑
1. 点击"查看原始需求"或"查看优化需求"
2. 在弹出的对话框中点击"编辑"
3. 使用Markdown编辑器进行编辑
4. 支持实时预览
5. 点击"保存"确认修改

### Markdown编辑器使用
- **工具栏**：提供常用格式化按钮
- **实时预览**：左侧编辑，右侧预览
- **语法支持**：支持标准Markdown语法
- **图片上传**：拖拽或点击上传图片
- **快捷键**：
  - `Ctrl + B`：加粗
  - `Ctrl + I`：斜体
  - `Ctrl + K`：插入链接
  - `Ctrl + S`：保存

## 🔍 代码Diff使用指南

### 功能概述
代码Diff模块支持Git仓库的分支对比和提交对比，生成可视化的代码差异报告。

### 基本操作

#### 1. 创建Diff任务
1. 进入 **代码Diff** → **Diff任务**
2. 点击"创建Diff任务"
3. 填写任务信息：
   - **任务名称**：描述性名称
   - **选择仓库**：从配置的Git仓库中选择
   - **对比类型**：分支对比或提交对比
   - **源分支/提交**：对比的源
   - **目标分支/提交**：对比的目标
4. 点击"创建"

#### 2. 执行Diff任务
1. 在任务列表中找到待执行的任务
2. 点击"执行"按钮
3. 等待任务完成
4. 查看执行状态和进度

#### 3. 查看Diff结果
1. 任务完成后点击"查看Diff"
2. 查看代码差异内容：
   - **绿色**：新增的代码
   - **红色**：删除的代码
   - **行号**：显示具体位置
3. 支持文件过滤和搜索

### 最佳实践
- **命名规范**：使用描述性的任务名称
- **定期清理**：删除不需要的历史任务
- **分支策略**：建议对比feature分支与main分支

## 🔄 流水线使用指南

### 功能概述
流水线模块是平台的核心功能，支持代码评审和测试用例生成的自动化流程。

### 基本操作

#### 1. 创建代码评审任务
1. 进入 **流水线** → **流水线任务**
2. 点击"创建代码评审任务"
3. 配置任务参数：
   - **任务名称**：描述性名称
   - **选择代码Diff**：从已有的Diff任务中选择
   - **选择需求文档**：关联相关需求
   - **Prompt模板**：选择代码评审模板
   - **知识库**：可选择相关知识库
4. 点击"创建并执行"

#### 2. 创建用例生成任务
1. 点击"创建用例生成任务"
2. 配置类似参数
3. 选择测试用例相关的Prompt模板
4. 执行任务

#### 3. 监控任务执行
1. 在任务列表中查看执行状态：
   - **待执行**：任务已创建，等待执行
   - **排队中**：任务在执行队列中
   - **执行中**：任务正在执行
   - **已完成**：任务执行完成
   - **失败**：任务执行失败
2. 点击任务名称查看详细信息

#### 4. 查看任务结果
1. 任务完成后点击"查看结果"
2. 代码评审结果包括：
   - **总体评价**：代码质量概述
   - **问题列表**：发现的问题和建议
   - **安全检查**：安全漏洞分析
   - **性能建议**：性能优化建议
3. 测试用例结果包括：
   - **用例列表**：生成的测试用例
   - **覆盖分析**：测试覆盖情况
   - **用例分类**：按功能分类的用例

### 高级功能

#### 批量操作
- 支持批量创建任务
- 批量执行和停止
- 批量导出结果

#### 结果导出
- 支持导出为Markdown格式
- 支持导出为Excel格式
- 可自定义导出模板

## ⚙️ 基础配置指南

### Git配置

#### 1. 配置Git凭证
1. 进入 **基础配置** → **Git配置** → **凭证管理**
2. 点击"添加凭证"
3. 填写Git用户信息：
   - **用户名**：Git用户名
   - **邮箱**：Git邮箱
   - **访问令牌**：Git平台的访问令牌
4. 测试连接并保存

#### 2. 配置Git仓库
1. 进入"仓库管理"标签
2. 点击"添加仓库"
3. 填写仓库信息：
   - **仓库别名**：便于识别的名称
   - **仓库地址**：Git仓库URL
   - **关联凭证**：选择对应的凭证
4. 测试连接并保存

### Prompt模板配置

#### 1. 创建模板
1. 进入 **基础配置** → **Prompt模板**
2. 点击"添加模板"
3. 填写模板信息：
   - **模板名称**：描述性名称
   - **唯一标识**：英文标识符
   - **分类**：选择模板分类
   - **标签**：添加相关标签
   - **模板内容**：使用Markdown编辑器编写
4. 设置公开/私有权限
5. 保存模板

#### 2. 使用变量
在模板中可以使用以下变量：
- `{{code_diff}}`：代码差异内容
- `{{requirement}}`：需求文档内容
- `{{context}}`：上下文信息
- `{{user_input}}`：用户输入内容

#### 3. 模板示例
```markdown
# 代码评审模板

你是一个资深的代码评审专家，请对以下代码进行详细评审：

## 代码差异
{{code_diff}}

## 相关需求
{{requirement}}

请从以下几个方面进行评审：
1. 代码质量和规范
2. 安全性检查
3. 性能优化建议
4. 可维护性分析
```

### 知识库管理

#### 1. 创建知识库
1. 进入 **基础配置** → **知识库**
2. 点击"创建知识库"
3. 填写知识库信息
4. 设置访问权限

#### 2. 上传文档
1. 选择知识库
2. 点击"上传文档"
3. 选择文档文件
4. 等待向量化处理完成

#### 3. 知识检索
- 在AI助手中可以引用知识库内容
- 支持语义搜索
- 自动关联相关文档

## 👤 个人中心使用指南

### 基本信息管理
1. 进入 **个人信息** 页面
2. 编辑基本信息：
   - 邮箱、昵称、部门、职位
3. 上传和更换头像
4. 保存修改

### 密码管理
1. 点击"修改密码"
2. 输入当前密码
3. 设置新密码
4. 确认修改

### 偏好设置
- 主题选择（明亮/暗黑）
- 语言设置
- 通知偏好

## 💡 使用技巧

### 提高效率的技巧

#### 1. 快捷键使用
- `Ctrl + Enter`：发送AI消息
- `Ctrl + /`：显示快捷键帮助
- `Ctrl + K`：快速搜索功能

#### 2. 模板复用
- 创建常用的Prompt模板
- 使用标签分类管理
- 分享优质模板给团队

#### 3. 批量操作
- 批量上传需求文档
- 批量创建流水线任务
- 批量导出结果

### 最佳实践

#### 1. 需求管理
- 使用结构化的需求描述
- 定期更新和维护需求
- 利用AI优化需求质量

#### 2. 代码评审
- 建立标准的评审流程
- 使用专业的评审模板
- 及时处理评审建议

#### 3. 团队协作
- 共享优质的Prompt模板
- 建立团队知识库
- 定期总结和分享经验

## 🚨 常见问题

### 登录问题
**Q: 忘记密码怎么办？**
A: 联系管理员重置密码，或使用密码重置功能。

**Q: 登录后页面空白？**
A: 检查浏览器兼容性，清除缓存后重试。

### AI功能问题
**Q: AI回复很慢或不回复？**
A: 检查AI模型配置，确认API密钥有效，网络连接正常。

**Q: Token消耗过快？**
A: 优化Prompt模板，减少不必要的上下文，选择合适的模型。

### 文件上传问题
**Q: 文件上传失败？**
A: 检查文件格式和大小限制，确保网络连接稳定。

**Q: 文档解析错误？**
A: 确认文档格式正确，内容清晰可读。

### 性能问题
**Q: 页面加载慢？**
A: 检查网络连接，清除浏览器缓存，关闭不必要的标签页。

**Q: 任务执行慢？**
A: 检查任务队列状态，避免同时执行过多任务。

## 📞 技术支持

### 联系方式
- **技术支持邮箱**：<EMAIL>
- **用户群**：微信群/QQ群
- **在线文档**：https://docs.example.com

### 反馈渠道
- **功能建议**：通过平台内反馈功能提交
- **Bug报告**：详细描述问题和复现步骤
- **使用咨询**：联系技术支持团队

---

**文档版本**：v2.0  
**最后更新**：2024年12月  
**负责人**：产品团队
