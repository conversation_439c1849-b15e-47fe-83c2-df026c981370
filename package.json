{"name": "front-platform", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/node": "^24.0.1", "axios": "^1.9.0", "element-plus": "^2.10.1", "md-editor-v3": "^5.6.1", "pinia": "^3.0.3", "sass": "^1.89.2", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}