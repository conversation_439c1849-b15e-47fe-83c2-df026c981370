import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface UserInfo {
  id: string
  username: string
  email: string
  avatar?: string
  roles: string[]
  nickname?: string
  phone?: string
  department?: string
  position?: string
  lastLoginTime?: string
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const hasRole = computed(() => (role: string) => {
    return userInfo.value?.roles.includes(role) || false
  })

  // 方法
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
  }

  const login = async (username: string, password: string) => {
    try {
      isLoading.value = true
      // TODO: 调用登录 API
      // const response = await loginApi({ username, password })
      // setToken(response.token)
      // setUserInfo(response.userInfo)
      
      // 临时模拟登录
      const mockToken = 'mock-jwt-token'
      const mockUserInfo: UserInfo = {
        id: '1',
        username,
        email: `${username}@example.com`,
        roles: ['admin']
      }
      
      setToken(mockToken)
      setUserInfo(mockUserInfo)
      
      return true
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
  }

  const refreshUserInfo = async () => {
    try {
      // TODO: 调用获取用户信息 API
      // const response = await getUserInfoApi()
      // setUserInfo(response)
    } catch (error) {
      console.error('Refresh user info failed:', error)
      logout()
    }
  }

  const initializeAuth = () => {
    // 从localStorage恢复token
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      setToken(savedToken)
      // 可以在这里调用refreshUserInfo来获取用户信息
      // refreshUserInfo()
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoading,

    // 计算属性
    isLoggedIn,
    hasRole,

    // 方法
    setToken,
    setUserInfo,
    login,
    logout,
    refreshUserInfo,
    initializeAuth
  }
})
