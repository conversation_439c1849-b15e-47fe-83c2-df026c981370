import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 导入布局组件
const Layout = () => import('@/components/layout/Layout.vue')

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '仪表盘',
          icon: 'Dashboard'
        }
      }
    ]
  },
  {
    path: '/configuration',
    component: Layout,
    redirect: '/configuration/git',
    meta: {
      title: '基础配置',
      icon: 'Setting'
    },
    children: [
      {
        path: 'git',
        name: 'GitConfig',
        component: () => import('@/views/configuration/GitConfig.vue'),
        meta: {
          title: 'Git配置',
          icon: 'Connection'
        }
      },
      {
        path: 'prompts',
        name: 'PromptTemplates',
        component: () => import('@/views/configuration/PromptTemplates.vue'),
        meta: {
          title: 'Prompt模板',
          icon: 'Document'
        }
      },
      {
        path: 'knowledge-base',
        name: 'KnowledgeBase',
        component: () => import('@/views/configuration/KnowledgeBase.vue'),
        meta: {
          title: '知识库',
          icon: 'Collection'
        }
      }
    ]
  },
  {
    path: '/code-review',
    component: Layout,
    redirect: '/code-review/tasks',
    meta: {
      title: '代码评审',
      icon: 'View'
    },
    children: [
      {
        path: 'tasks',
        name: 'ReviewTaskList',
        component: () => import('@/views/code-review/ReviewTaskList.vue'),
        meta: {
          title: '评审任务',
          icon: 'List'
        }
      },
      {
        path: 'create',
        name: 'CreateReviewTask',
        component: () => import('@/views/code-review/CreateReviewTask.vue'),
        meta: {
          title: '创建评审任务',
          icon: 'Plus'
        }
      },
      {
        path: 'report/:id',
        name: 'ReviewReport',
        component: () => import('@/views/code-review/ReviewReport.vue'),
        meta: {
          title: '评审报告',
          icon: 'Document'
        }
      }
    ]
  },
  {
    path: '/test-case',
    component: Layout,
    redirect: '/test-case/tasks',
    meta: {
      title: '用例生成',
      icon: 'Files'
    },
    children: [
      {
        path: 'tasks',
        name: 'CaseTaskList',
        component: () => import('@/views/test-case/CaseTaskList.vue'),
        meta: {
          title: '用例任务',
          icon: 'List'
        }
      },
      {
        path: 'create',
        name: 'CreateCaseTask',
        component: () => import('@/views/test-case/CreateCaseTask.vue'),
        meta: {
          title: '创建用例任务',
          icon: 'Plus'
        }
      },
      {
        path: 'management',
        name: 'CaseManagement',
        component: () => import('@/views/test-case/CaseManagement.vue'),
        meta: {
          title: '用例管理',
          icon: 'Management'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 导航守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - AI研发辅助平台`
  }
  
  // 检查是否需要登录
  if (to.meta?.requiresAuth !== false && !userStore.isLoggedIn) {
    next('/login')
    return
  }
  
  // 如果已登录且访问登录页，重定向到首页
  if (to.path === '/login' && userStore.isLoggedIn) {
    next('/')
    return
  }
  
  next()
})

export default router
