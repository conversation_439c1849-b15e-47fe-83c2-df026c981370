// 基础类型定义
export interface BaseModel {
  id: string
  createdAt: string
  updatedAt: string
}

// Git 配置相关类型
export interface GitCredential {
  username: string
  token: string
}

export interface GitRepository extends BaseModel {
  alias: string
  url: string
  defaultBaseBranch?: string
}

// Prompt 模板相关类型
export interface PromptTemplate extends BaseModel {
  name: string
  identifier: string
  content: string
  description?: string
}

// 知识库相关类型
export interface KnowledgeBase extends BaseModel {
  name: string
  description?: string
}

export interface KnowledgeDocument extends BaseModel {
  knowledgeBaseId: string
  filename: string
  fileType: string
  fileSize: number
  status: 'uploading' | 'processing' | 'completed' | 'failed'
}

// 任务相关类型
export type TaskStatus = 'pending' | 'queued' | 'running' | 'completed' | 'failed'

export interface CodeDiff extends BaseModel {
  repositoryId: string
  sourceRef: string
  targetRef: string
  diffContent: string
  compareType: 'branch' | 'commit'
}

export interface RequirementText extends BaseModel {
  content: string
  source: 'upload' | 'manual'
  originalFilename?: string
}

export interface ReviewTask extends BaseModel {
  name: string
  codeDiffId: string
  requirementTextId: string
  promptTemplateId: string
  knowledgeBaseId?: string
  status: TaskStatus
  result?: string
  errorMessage?: string
}

export interface TestCaseTask extends BaseModel {
  name: string
  requirementTextId: string
  promptTemplateId: string
  caseTemplateId?: string
  status: TaskStatus
  result?: TestCase[]
  errorMessage?: string
}

export interface TestCase extends BaseModel {
  title: string
  preconditions: string
  steps: string
  expectedResult: string
  priority: 'P0' | 'P1' | 'P2'
}

export interface CaseTemplate extends BaseModel {
  name: string
  structure: Record<string, string>
}
