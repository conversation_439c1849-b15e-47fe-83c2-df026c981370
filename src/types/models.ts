// 基础类型定义
export interface BaseModel {
  id: string
  createdAt: string
  updatedAt: string
}

// Git 配置相关类型
export interface GitCredential {
  username: string
  token: string
}

export interface GitRepository extends BaseModel {
  alias: string
  url: string
  defaultBaseBranch?: string
}

// Prompt 模板相关类型
export interface PromptTemplate extends BaseModel {
  name: string
  identifier: string
  content: string
  description?: string
  tags: string[]
  category: 'requirement' | 'code_review' | 'test_case' | 'general'
  variables: string[]
  isPublic: boolean
  usageCount: number
}

// 知识库相关类型
export interface KnowledgeBase extends BaseModel {
  name: string
  description?: string
}

export interface KnowledgeDocument extends BaseModel {
  knowledgeBaseId: string
  filename: string
  fileType: string
  fileSize: number
  status: 'uploading' | 'processing' | 'completed' | 'failed'
}

// 任务相关类型
export type TaskStatus = 'pending' | 'queued' | 'running' | 'completed' | 'failed'

// 代码Diff相关类型
export interface CodeDiffTask extends BaseModel {
  name: string
  repositoryId: string
  sourceRef: string
  targetRef: string
  compareType: 'branch' | 'commit'
  status: TaskStatus
  diffContent?: string
  errorMessage?: string
}

// 需求文档相关类型
export interface RequirementDocument extends BaseModel {
  name: string
  originalContent: string
  optimizedContent?: string
  source: 'upload' | 'manual'
  originalFilename?: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  parseTaskId?: string
}

// 流水线任务相关类型
export interface PipelineTask extends BaseModel {
  name: string
  type: 'code_review' | 'test_case'
  codeDiffTaskId?: string
  requirementDocumentId?: string
  promptTemplateId: string
  knowledgeBaseId?: string
  status: TaskStatus
  result?: string
  errorMessage?: string
}

export interface TestCaseTask extends BaseModel {
  name: string
  requirementTextId: string
  promptTemplateId: string
  caseTemplateId?: string
  status: TaskStatus
  result?: TestCase[]
  errorMessage?: string
}

export interface TestCase extends BaseModel {
  title: string
  preconditions: string
  steps: string
  expectedResult: string
  priority: 'P0' | 'P1' | 'P2'
}

export interface CaseTemplate extends BaseModel {
  name: string
  structure: Record<string, string>
}

// AI大模型配置相关类型
export interface AIModelConfig extends BaseModel {
  name: string
  provider: 'openai' | 'deepseek' | 'spark' | 'doubao' | 'gemini' | 'claude'
  baseUrl: string
  apiKey: string
  model: string
  maxTokens?: number
  temperature?: number
  isDefault: boolean
  isActive: boolean
}

// AI对话相关类型
export interface AIMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  modelConfig?: string
  promptTemplate?: string
  tokens?: number
}

export interface AIConversation extends BaseModel {
  title: string
  messages: AIMessage[]
  modelConfigId: string
  totalTokens: number
  lastMessageAt: string
}

// Prompt模板标签
export interface PromptTag {
  id: string
  name: string
  color: string
  category: 'requirement' | 'code_review' | 'test_case' | 'general'
}
