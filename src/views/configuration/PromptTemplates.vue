<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">Prompt模板管理</h1>
      <p class="page-description">创建和管理高质量的Prompt模板，支持链式调用</p>
    </div>

    <el-card>
      <template #header>
        <div class="card-header">
          <span>模板列表</span>
          <el-button type="primary" @click="showAddDialog">
            新建模板
          </el-button>
        </div>
      </template>
      
      <el-table :data="templates" style="width: 100%">
        <el-table-column prop="name" label="模板名称" />
        <el-table-column prop="identifier" label="唯一标识" />
        <el-table-column prop="description" label="说明" show-overflow-tooltip />
        <el-table-column prop="updatedAt" label="更新时间" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button type="text" @click="editTemplate(scope.row)">
              编辑
            </el-button>
            <el-button type="text" @click="deleteTemplate(scope.row)" style="color: #f56c6c">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑模板对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑模板' : '新建模板'"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="templateFormRef"
        :model="templateForm"
        :rules="templateRules"
        label-width="120px"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input
            v-model="templateForm.name"
            placeholder="请输入模板名称"
          />
        </el-form-item>
        
        <el-form-item label="唯一标识" prop="identifier">
          <el-input
            v-model="templateForm.identifier"
            placeholder="请输入英文标识，如：code_review_security"
          />
        </el-form-item>
        
        <el-form-item label="模板内容" prop="content">
          <el-input
            v-model="templateForm.content"
            type="textarea"
            :rows="10"
            placeholder="请输入Prompt内容，支持变量占位符如{{code_diff}}，支持链式调用如{{GENERATE_FROM:template_id}}"
          />
        </el-form-item>
        
        <el-form-item label="说明">
          <el-input
            v-model="templateForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板说明，包括用途、适用场景和变量说明"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveTemplate" :loading="saving">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import type { PromptTemplate } from '@/types'

// 表单引用
const templateFormRef = ref<FormInstance>()

// 加载状态
const saving = ref(false)

// 模板列表
const templates = ref<PromptTemplate[]>([])

// 对话框状态
const dialogVisible = ref(false)
const isEdit = ref(false)

// 模板表单
const templateForm = reactive({
  id: '',
  name: '',
  identifier: '',
  content: '',
  description: ''
})

// 表单验证规则
const templateRules: FormRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  identifier: [
    { required: true, message: '请输入唯一标识', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '标识必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入模板内容', trigger: 'blur' }
  ]
}

// 显示添加对话框
const showAddDialog = () => {
  isEdit.value = false
  Object.assign(templateForm, {
    id: '',
    name: '',
    identifier: '',
    content: '',
    description: ''
  })
  dialogVisible.value = true
}

// 编辑模板
const editTemplate = (template: PromptTemplate) => {
  isEdit.value = true
  Object.assign(templateForm, template)
  dialogVisible.value = true
}

// 保存模板
const saveTemplate = async () => {
  if (!templateFormRef.value) return
  
  try {
    await templateFormRef.value.validate()
    saving.value = true
    
    // TODO: 调用API保存模板
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '模板更新成功' : '模板创建成功')
    dialogVisible.value = false
    loadTemplates()
  } catch (error) {
    console.error('Save template failed:', error)
  } finally {
    saving.value = false
  }
}

// 删除模板
const deleteTemplate = async (template: PromptTemplate) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板 "${template.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 调用API删除模板
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('模板删除成功')
    loadTemplates()
  } catch {
    // 用户取消
  }
}

// 加载模板列表
const loadTemplates = async () => {
  try {
    // TODO: 调用API加载模板列表
    // 模拟数据
    templates.value = [
      {
        id: '1',
        name: '代码评审-安全漏洞扫描',
        identifier: 'code_review_security',
        content: '你是一个代码安全专家，请仔细分析以下代码变更，识别潜在的安全漏洞...',
        description: '专门用于检测代码中的安全问题',
        createdAt: '2024-01-15 10:30:00',
        updatedAt: '2024-01-15 10:30:00'
      }
    ]
  } catch (error) {
    console.error('Load templates failed:', error)
  }
}

onMounted(() => {
  loadTemplates()
})
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
